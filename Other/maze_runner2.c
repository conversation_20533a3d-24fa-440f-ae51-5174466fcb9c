/*  Maze Runner – Complete C implementation of the rules in the assignment
 *
 *  Applies: floors, shapes, stairs, poles, walls, Bawana, consumables/bonuses,
 *  movement points, captures, directional dice cadence, and mid-move stair/pole triggers.
 *
 *  Where the PDF is ambiguous on geometry/durations, defaults are chosen and
 *  clearly marked as tweakable constants below.
 *
 *  Build:  gcc -std=c11 -O2 maze_runner_full.c -o maze_runner
 *  Run:    ./maze_runner [seed]
 *
 *  Author’s notes:
 *  - Floor indexing: 0=ground, 1=middle, 2=top.
 *  - Coordinates: [floor, width (x: 0..9 left→right), length (y: 0..24 top→bottom)].
 *  - Directions: NORTH (y-1), EAST (x+1), SOUTH (y+1), WEST (x-1).
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>

/* ===================== TWEAKABLE CONSTANTS ===================== */

#define FLOORS 3
#define W      10
#define L      25

/* Starting area (floor 0): rectangle 4×9 placed along “south border”.
   Matches the given player start cells: width 6..9, length 8..16. */
#define START_W_MIN 6
#define START_W_MAX 9
#define START_L_MIN 8
#define START_L_MAX 16

/* Floor-2 (index 1) geometry:
   Two 10×8 rectangles (length bands 0..7 and 16..23) plus a 4×9 bridge (width 6..9, length 8..16). */
#define F1_RECT1_L0 0
#define F1_RECT1_L1 7
#define F1_RECT2_L0 16
#define F1_RECT2_L1 23
#define F1_BRIDGE_W0 6
#define F1_BRIDGE_W1 9
#define F1_BRIDGE_L0 8
#define F1_BRIDGE_L1 16

/* Floor-3 (index 2) geometry: 10×9 rectangle above starting area (length 8..16). */
#define F2_RECT_L0 8
#define F2_RECT_L1 16

/* Bawana region (floor 0): width 6..9 and length 20..23, with one-way exit at (9,19). */
#define BAW_W0 6
#define BAW_W1 9
#define BAW_L0 20
#define BAW_L1 23
#define BAW_EXIT_W 9
#define BAW_EXIT_L 19

/* Stairs-direction rule (Rule 6): how often to randomize and for how many rounds it stays one-way */
#define STAIRS_CHANGE_EVERY_ROUNDS 5
#define STAIRS_ONEWAY_DURATION_ROUNDS 2   /* unidirectional for this many rounds after each change */

/* “Triggered” duration not specified; align with Disoriented’s 4 throws for symmetry and tweakability. */
#define DISORIENTED_THROWS 4
#define TRIGGERED_THROWS   4

/* Maximum number of rounds (a round = all 3 players take a turn) to avoid infinite games in testing. */
#define MAX_ROUNDS 1000

/* Verbosity: 0 minimal, 1 normal, 2 detailed per-step log. */
#define VERBOSITY 1

/* ===================== TYPES & UTILITIES ===================== */

typedef enum { NORTH=0, EAST=1, SOUTH=2, WEST=3 } Dir;
static const int dW[4] = { 0,  1,  0, -1 };
static const int dL[4] = {-1,  0,  1,  0 };

typedef enum { CELL_VOID=0, CELL_NORMAL, CELL_START, CELL_BAWANA } CellKind;

typedef struct {
    CellKind kind;
    signed char consumable;  /* -1 means N/A; otherwise 0..4 */
    signed char bonus_add;   /* 0 means none; otherwise 1..5 */
    signed char bonus_mul;   /* 1 means none; otherwise 2 or 3 */
} Cell;

typedef struct { int f, x, y; } Pos;

typedef struct {
    int sf, sx, sy, ef, ex, ey;   /* start floor/coord → end floor/coord */
} Stair;

typedef enum { STAIR_BIDIR=0, STAIR_UP_ONLY, STAIR_DOWN_ONLY } StairMode;

typedef struct {
    Stair base;
    StairMode mode;               /* active mode (may switch due to Rule 6) */
} StairState;

typedef struct {
    int f_start, f_end, x, y;     /* floor span [f_start..f_end], at (x,y); drop from higher→lower to f_start */
} Pole;

typedef struct {
    char name;
    Pos pos;                   /* current position (if in_maze=false, this is the start cell in the area) */
    Pos first_cell;            /* the first maze cell to enter after rolling a 6 */
    Dir dir;
    bool in_maze;

    int mp;                    /* movement points */
    int throws_since_dir;      /* 1..4; on 4th throw, direction die is rolled */
    int turns_skipped;         /* Food Poisoning: skip this many turns; after it, re-enter Bawana */
    int disoriented_left;      /* next N throws use random directions */
    int triggered_left;        /* next N throws move double steps */
} Player;

/* ===================== GLOBAL STATE ===================== */

static Cell G[FLOORS][W][L];
static bool WALL[FLOORS][W][L][4];   /* per-cell edge walls in N/E/S/W */

/* One-way gate for Bawana exit: allow from (9,20)->(9,19), but disallow (9,19)->(9,20) */
static bool ALLOW_ONEWAY_EXIT = true;

/* Stairs & poles – sample placements (tweak easily). Ensure endpoints are valid cells. */
#define N_STAIRS 6
static StairState STAIRS[N_STAIRS] = {
    /* Example from brief adapted: [0,4,5] → [2,0,10] */
    {{0,4,5,  2,0,10}, STAIR_BIDIR},

    /* A few more reasonable connections across shapes/bridge */
    {{0,8,10, 1,8,10}, STAIR_BIDIR}, /* ground → bridge center */
    {{1,7,3,  2,7,8 }, STAIR_BIDIR}, /* mid north rect → top rect edge */
    {{0,2,20, 1,2,18}, STAIR_BIDIR}, /* ground south area → mid bridge */
    {{1,1,20, 2,1,16}, STAIR_BIDIR}, /* mid south rect → top rect */
    {{0,9,17, 1,9,16}, STAIR_BIDIR}, /* near C’s entry upwards */
};

#define N_POLES 2
static Pole POLES[N_POLES] = {
    /* Example semantics: a pole spanning floors 0..2 at (5,24): landing on (5,24) on f=1 or 2 drops to f=0 */
    {0,2,5,24},
    /* Another pole spanning floors 0..1 at (9,7): dropping to ground if you hit (9,7) from floor 1 */
    {0,1,9,7},
};

/* Random placement of the flag on any valid, non-Bawana, non-start cell */
static Pos FLAG;

/* Round counting to implement Rule 6 (stair one-way phases) */
static int current_round = 0;
static int stair_oneway_rounds_left = 0;

/* ===================== RANDOM ===================== */
static inline int rnd(int lo, int hi) { /* inclusive */
    return lo + (rand() % (hi - lo + 1));
}

/* ===================== HELPERS ===================== */

/* --- helpers --- */
static inline int is_valid_dir(int d) { return d>=0 && d<=3; }

static const char* dir_name(Dir d) {
    switch (d) {
        case NORTH: return "NORTH";
        case EAST:  return "EAST";
        case SOUTH: return "SOUTH";
        case WEST:  return "WEST";
        default:    return "NORTH"; /* safe fallback for any bad value */
    }
}

/* Return new direction OR -1 to mean "empty face → keep current direction". */
static int parse_direction_die(int face) {
    switch (face) {
        /* 1 and 6 are EMPTY per spec */
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1;
    }
}


static bool inside_board(int x, int y) { return (x>=0 && x<W && y>=0 && y<L); }

static bool cell_is_valid_shape(int f, int x, int y) {
    if (!inside_board(x,y)) return false;
    if (f==0) {
        return true; /* whole 10×25 exists */
    } else if (f==1) {
        /* two 10×8 rectangles + 4×9 bridge */
        if (y>=F1_RECT1_L0 && y<=F1_RECT1_L1) return true;
        if (y>=F1_RECT2_L0 && y<=F1_RECT2_L1) return true;
        if (x>=F1_BRIDGE_W0 && x<=F1_BRIDGE_W1 && y>=F1_BRIDGE_L0 && y<=F1_BRIDGE_L1) return true;
        return false;
    } else if (f==2) {
        if (y>=F2_RECT_L0 && y<=F2_RECT_L1) return true;
        return false;
    }
    return false;
}

static bool cell_exists(int f, int x, int y) {
    return cell_is_valid_shape(f,x,y) && (G[f][x][y].kind != CELL_VOID);
}

static void add_wall_between(int f, int x1, int y1, int x2, int y2) {
    /* place a wall on the shared edge between adjacent cells (same floor).
       Works for horiz or vert adjacency. */
    if (!inside_board(x1,y1) || !inside_board(x2,y2)) return;
    if (abs(x1-x2) + abs(y1-y2) != 1) return; /* must be adjacent */
    if (x2 == x1+1 && y2==y1) { WALL[f][x1][y1][EAST]  = true; WALL[f][x2][y2][WEST]  = true; }
    if (x2 == x1-1 && y2==y1) { WALL[f][x1][y1][WEST]  = true; WALL[f][x2][y2][EAST]  = true; }
    if (y2 == y1+1 && x2==x1) { WALL[f][x1][y1][SOUTH] = true; WALL[f][x2][y2][NORTH] = true; }
    if (y2 == y1-1 && x2==x1) { WALL[f][x1][y1][NORTH] = true; WALL[f][x2][y2][SOUTH] = true; }
}

static void init_walls_and_boundaries(void) {
    memset(WALL, 0, sizeof(WALL));
    /* Outer boundaries & shape holes act as walls */
    for (int f=0; f<FLOORS; ++f) {
        for (int x=0; x<W; ++x) for (int y=0; y<L; ++y) {
            if (!cell_is_valid_shape(f,x,y)) continue;
            for (int d=0; d<4; ++d) {
                int nx = x + dW[d], ny = y + dL[d];
                if (!inside_board(nx,ny) || !cell_is_valid_shape(f,nx,ny)) {
                    WALL[f][x][y][d] = true;
                }
            }
        }
    }

    /* Bawana boundary walls on floor 0:
       - horizontal wall along y=20 from x=6..9 (blocks between y=19 and y=20)
       - vertical wall along x=6 from y=20..24 (blocks between x=5 and x=6)
       Then allow a one-way exit at (9,20)->(9,19) only.
    */
    int f=0;
    /* Horizontal segment y=20, x=6..9: block N/S between (x,19) and (x,20) */
    for (int x=BAW_W0; x<=BAW_W1; ++x) {
        if (inside_board(x,19) && inside_board(x,20)) {
            WALL[f][x][20][NORTH] = true;
            WALL[f][x][19][SOUTH] = true;
        }
    }
    /* Vertical segment x=6, y=20..24: block W/E between (5,y) and (6,y) */
    for (int y=BAW_L0; y<=L-1; ++y) {
        int x=BAW_W0;
        if (inside_board(x, y)) {
            WALL[f][x][y][WEST] = true;
            if (x-1>=0) WALL[f][x-1][y][EAST] = true;
        }
    }
    /* One-way exit at (9,20)->(9,19) : permit from inside to outside only */
    if (ALLOW_ONEWAY_EXIT) {
        if (inside_board(BAW_EXIT_W, 20) && inside_board(BAW_EXIT_W, BAW_EXIT_L)) {
            WALL[f][BAW_EXIT_W][20][NORTH] = false; /* allow leaving */
            WALL[f][BAW_EXIT_W][BAW_EXIT_L][SOUTH] = true;  /* block entry */
        }
    }
}

/* ===================== BOARD CELLS & EFFECTS ===================== */

typedef struct { int x,y,f; } Coord;

static void set_cell(Cell *c, CellKind kind, int cons, int add, int mul) {
    c->kind = kind; c->consumable = cons; c->bonus_add = add; c->bonus_mul = mul;
}

static void init_cells_and_shapes(void) {
    for (int f=0; f<FLOORS; ++f)
        for (int x=0; x<W; ++x)
            for (int y=0; y<L; ++y)
                set_cell(&G[f][x][y], CELL_VOID, -1, 0, 1);

    /* Valid shapes become normal cells (we’ll mark START/BAWANA later) */
    for (int f=0; f<FLOORS; ++f)
        for (int x=0; x<W; ++x)
            for (int y=0; y<L; ++y)
                if (cell_is_valid_shape(f,x,y)) set_cell(&G[f][x][y], CELL_NORMAL, 0, 0, 1);

    /* Starting area on floor 0 */
    for (int x=START_W_MIN; x<=START_W_MAX; ++x)
        for (int y=START_L_MIN; y<=START_L_MAX; ++y)
            set_cell(&G[0][x][y], CELL_START, -1, 0, 1);

    /* Bawana on floor 0 */
    for (int x=BAW_W0; x<=BAW_W1; ++x)
        for (int y=BAW_L0; y<=BAW_L1; ++y)
            set_cell(&G[0][x][y], CELL_BAWANA, -1, 0, 1);
}

static void shuffle(Coord *a, int n) {
    for (int i=n-1; i>0; --i) {
        int j = rnd(0,i);
        Coord t=a[i]; a[i]=a[j]; a[j]=t;
    }
}

static void assign_consumables_and_bonuses(void) {
    /* Apply Rule 10 distribution across all normal (non-START, non-BAWANA) cells */
    Coord cells[W*L*FLOORS];
    int n=0;
    for (int f=0; f<FLOORS; ++f)
        for (int x=0; x<W; ++x)
            for (int y=0; y<L; ++y) {
                if (!cell_exists(f,x,y)) continue;
                if (G[f][x][y].kind != CELL_NORMAL) continue; /* skip START/BAWANA */
                cells[n++] = (Coord){x,y,f};
            }

    shuffle(cells, n);

    int c0 = (int)(0.25*n);  /* consumable 0 */
    int c1 = (int)(0.35*n);  /* consumable 1..4 */
    int b12 = (int)(0.25*n); /* +1..+2 */
    int b35 = (int)(0.10*n); /* +3..+5 */
    int m23 = n - (c0 + c1 + b12 + b35); /* ×2/×3 */

    int idx=0;

    /* consumable 0 */
    for (int i=0; i<c0 && idx<n; ++i,++idx) {
        Coord c = cells[idx];
        set_cell(&G[c.f][c.x][c.y], CELL_NORMAL, 0, 0, 1);
    }
    /* consumable 1..4 */
    for (int i=0; i<c1 && idx<n; ++i,++idx) {
        Coord c = cells[idx];
        set_cell(&G[c.f][c.x][c.y], CELL_NORMAL, rnd(1,4), 0, 1);
    }
    /* bonuses +1..+2 */
    for (int i=0; i<b12 && idx<n; ++i,++idx) {
        Coord c = cells[idx];
        set_cell(&G[c.f][c.x][c.y], CELL_NORMAL, -1, rnd(1,2), 1);
    }
    /* bonuses +3..+5 */
    for (int i=0; i<b35 && idx<n; ++i,++idx) {
        Coord c = cells[idx];
        set_cell(&G[c.f][c.x][c.y], CELL_NORMAL, -1, rnd(3,5), 1);
    }
    /* multipliers ×2/×3 */
    for (; idx<n; ++idx) {
        Coord c = cells[idx];
        set_cell(&G[c.f][c.x][c.y], CELL_NORMAL, -1, 0, rnd(2,3));
    }
}

/* ===================== BAWANA ===================== */

typedef enum { BW_FOOD=0, BW_DISOR, BW_TRIG, BW_HAPPY, BW_POINTS } BwType;
static BwType BAWANA_MAP[BAW_W1-BAW_W0+1][BAW_L1-BAW_L0+1]; /* 4×4 types */

static void init_bawana_cells(void) {
    /* Create multiset: 3× each of FOOD, DISOR, TRIG, HAPPY + 4× POINTS */
    BwType bag[16];
    int k=0;
    for (int i=0;i<3;++i) bag[k++]=BW_FOOD;
    for (int i=0;i<3;++i) bag[k++]=BW_DISOR;
    for (int i=0;i<3;++i) bag[k++]=BW_TRIG;
    for (int i=0;i<3;++i) bag[k++]=BW_HAPPY;
    for (int i=0;i<4;++i) bag[k++]=BW_POINTS;

    /* Shuffle and lay into 4×4 (w-major) */
    for (int i=15;i>0;--i){int j=rnd(0,i); BwType t=bag[i]; bag[i]=bag[j]; bag[j]=t;}
    k=0;
    for (int x=0;x<=BAW_W1-BAW_W0;++x)
        for (int y=0;y<=BAW_L1-BAW_L0;++y)
            BAWANA_MAP[x][y] = bag[k++];
}

static BwType random_bawana_cell(void) {
    int rx=rnd(0,BAW_W1-BAW_W0), ry=rnd(0,BAW_L1-BAW_L0);
    return BAWANA_MAP[rx][ry];
}

static void place_exit(Player* P) {
    P->pos = (Pos){0, BAW_EXIT_W, BAW_EXIT_L};
    P->dir = NORTH; /* “always North” when exiting */
}

/* Entering Bawana due to MP<=0 or other transport.
   Applies the drawn effect instantly. */
static void apply_bawana_effect(Player* P) {
    BwType t = random_bawana_cell();
    switch (t) {
        case BW_FOOD:
            P->turns_skipped = 3;
            if (VERBOSITY) printf("  -> BAWANA: Food Poisoning (skip 3 turns). Will re-draw Bawana after that.\n");
            break;
        case BW_DISOR:
            P->mp += 50;
            P->disoriented_left = DISORIENTED_THROWS;
            place_exit(P);
            if (VERBOSITY) printf("  -> BAWANA: Disoriented (+50 MP). Next %d throws random directions.\n", DISORIENTED_THROWS);
            break;
        case BW_TRIG:
            P->mp += 50;
            P->triggered_left = TRIGGERED_THROWS;
            place_exit(P);
            if (VERBOSITY) printf("  -> BAWANA: Triggered (+50 MP). Next %d throws move doubled.\n", TRIGGERED_THROWS);
            break;
        case BW_HAPPY:
            P->mp += 200;
            place_exit(P);
            if (VERBOSITY) printf("  -> BAWANA: Happy (+200 MP). No other effects.\n");
            break;
        case BW_POINTS: {
            int pts = rnd(10,100);
            P->mp += pts;
            place_exit(P);
            if (VERBOSITY) printf("  -> BAWANA: Bonus points +%d MP.\n", pts);
            break;
        }
    }
}

/* ===================== STAIRS & POLES ===================== */

static void randomize_stairs_oneway(void) {
    for (int i=0;i<N_STAIRS;++i) {
        int r = rnd(0,1);
        STAIRS[i].mode = (r==0? STAIR_UP_ONLY : STAIR_DOWN_ONLY);
    }
    stair_oneway_rounds_left = STAIRS_ONEWAY_DURATION_ROUNDS;
}

static void restore_stairs_bidir(void) {
    for (int i=0;i<N_STAIRS;++i) STAIRS[i].mode = STAIR_BIDIR;
}

/* If standing on a stair endpoint, decide whether it triggers and where to go. */
static bool try_stair(Pos cur, Pos* out) {
    for (int i=0;i<N_STAIRS;++i) {
        StairState *S = &STAIRS[i];
        Pos dst;
        if (cur.f==S->base.sf && cur.x==S->base.sx && cur.y==S->base.sy) {
            if (S->mode==STAIR_DOWN_ONLY) continue;
            dst = (Pos){S->base.ef, S->base.ex, S->base.ey};
        } else if (cur.f==S->base.ef && cur.x==S->base.ex && cur.y==S->base.ey) {
            if (S->mode==STAIR_UP_ONLY) continue;
            dst = (Pos){S->base.sf, S->base.sx, S->base.sy};
        } else continue;

        if (cell_exists(dst.f, dst.x, dst.y)) { *out = dst; return true; }
    }
    return false;
}

/* If standing at a pole coordinate on f in (f_start..f_end], you drop to f_start. */
static bool try_pole(Pos cur, Pos* out) {
    for (int i=0;i<N_POLES;++i) {
        Pole *P = &POLES[i];
        if (cur.x==P->x && cur.y==P->y && cur.f>=P->f_start+1 && cur.f<=P->f_end) {
            *out = (Pos){P->f_start, cur.x, cur.y};
            return true;
        }
    }
    return false;
}

/* ===================== MOVEMENT & RULES ===================== */

/* Treat invalid directions as "blocked" so we never index WALL[..., dir] OOB. */
static bool edge_blocked(const Pos a, Dir d) {
    if (!cell_exists(a.f, a.x, a.y)) return true;
    if (!is_valid_dir(d)) return true;
    return WALL[a.f][a.x][a.y][d];
}

static bool attempt_full_move(Player* P, int steps, Player* A, Player* B, Player* C, bool *teleported_to_bawana);

/* Applies cell’s consumable/bonus/multiplier when the player enters it. */
static void apply_cell_effects(Player* P, const Cell* C) {
    if (C->consumable >= 0) P->mp -= C->consumable;
    else if (C->bonus_add > 0) P->mp += C->bonus_add;
    else if (C->bonus_mul > 1) P->mp *= C->bonus_mul;
}

/* Check capture at end of a successful move: if P landed on Q, send Q back to start. */
static void check_capture(Player* P, Player* Q) {
    if (!Q->in_maze) return;
    if (P->pos.f==Q->pos.f && P->pos.x==Q->pos.x && P->pos.y==Q->pos.y) {
        if (VERBOSITY) printf("  -> %c captures %c! %c sent back to starting cell.\n",
                               P->name, Q->name, Q->name);
        Q->in_maze = false;
        /* Keep Q’s MP as-is per spec (not specified to reset). Place back to start area cell. */
        /* P’s dir unchanged; captured player re-enters later by rolling a six. */
    }
}

/* Simulate the movement; commit only if all steps can be completed (walls/edges).
   If MP falls to <=0 mid-move, transport to Bawana immediately and commit progress. */
static bool attempt_full_move(Player* P, int steps, Player* A, Player* B, Player* C, bool *teleported_to_bawana) {
    *teleported_to_bawana = false;

    Pos start = P->pos;
    int mp_start = P->mp;

    Pos cur = start;
    int remaining = steps;

    while (remaining > 0) {
        /* Step one cell in current direction */
        if (edge_blocked(cur, P->dir)) {
            /* blocked by wall/edge */
            if (VERBOSITY>=2) printf("    blocked at [%d,%d,%d]\n", cur.f,cur.x,cur.y);
            /* restore & no move */
            P->pos = start;
            P->mp  = mp_start;
            return false;
        }
        Pos nxt = (Pos){cur.f, cur.x + dW[P->dir], cur.y + dL[P->dir]};
        if (!cell_exists(nxt.f, nxt.x, nxt.y)) {
            /* falls off shape */
            if (VERBOSITY>=2) printf("    invalid dest\n");
            P->pos = start;
            P->mp  = mp_start;
            return false;
        }

        /* Move into nxt */
        cur = nxt;
        const Cell* Cc = &G[cur.f][cur.x][cur.y];

        /* Rule 4 (advanced): stairs/poles can trigger mid-move */
        Pos jump;
        if (try_stair(cur, &jump)) {
            if (VERBOSITY>=2) printf("    stair jump to [%d,%d,%d]\n", jump.f,jump.x,jump.y);
            cur = jump;
        } else if (try_pole(cur, &jump)) {
            if (VERBOSITY>=2) printf("    pole drop to [%d,%d,%d]\n", jump.f,jump.x,jump.y);
            cur = jump;
        }

        /* Apply cell effects (Rule 8/9/10) */
        apply_cell_effects(P, &G[cur.f][cur.x][cur.y]);

        /* If MP depleted: transport to Bawana immediately (Rule 11) */
        if (P->mp <= 0) {
            if (VERBOSITY) printf("  -> MP depleted mid-move. Transported to Bawana.\n");
            P->pos = cur; /* commit position before transport for clarity */
            apply_bawana_effect(P);
            *teleported_to_bawana = true;
            return true; /* movement resolves */
        }

        --remaining;
    }

    /* Completed all steps; commit */
    P->pos = cur;
    return true;
}

/* On a blocked move (couldn’t do all steps), pay 2 MP (Rule 12) */
static void pay_blocked_cost(Player* P) {
    P->mp -= 2;
    if (VERBOSITY) printf("  -> Move blocked. Paid 2 MP (Rule 12). MP=%d\n", P->mp);
    if (P->mp <= 0) {
        if (VERBOSITY) printf("  -> MP now <=0 due to blockage cost. Transported to Bawana.\n");
        apply_bawana_effect(P);
    }
}



/* ===================== GAME SETUP ===================== */

static void place_random_flag(void) {
    while (1) {
        int f=rnd(0,2), x=rnd(0,9), y=rnd(0,24);
        if (!cell_exists(f,x,y)) continue;
        if (G[f][x][y].kind==CELL_START || G[f][x][y].kind==CELL_BAWANA) continue;
        FLAG = (Pos){f,x,y};
        break;
    }
}

static void init_player(Player* P, char name, int sx, int sy, int fx, int fy, Dir d) {
    P->name = name;
    P->pos = (Pos){0,sx,sy};
    P->first_cell = (Pos){0,fx,fy};
    P->dir = d;
    P->in_maze = false;
    P->mp = 100;
    P->throws_since_dir = 0;
    P->turns_skipped = 0;
    P->disoriented_left = 0;
    P->triggered_left = 0;
}

static void setup_players(Player* A, Player* B, Player* C) {
    init_player(A, 'A', 6,12, 5,12, NORTH);
    init_player(B, 'B', 9, 8, 9, 7, WEST);
    init_player(C, 'C', 9,16, 9,17, EAST);
}

/* ===================== MAIN LOOP ===================== */

static bool same_pos(const Pos a, const Pos b){return a.f==b.f && a.x==b.x && a.y==b.y;}

int main(int argc, char** argv) {
    unsigned seed = (argc>=2? (unsigned)strtoul(argv[1],NULL,10) : (unsigned)time(NULL));
    srand(seed);

    init_cells_and_shapes();
    init_walls_and_boundaries();
    assign_consumables_and_bonuses();
    init_bawana_cells();
    place_random_flag();

    Player A,B,C;
    setup_players(&A,&B,&C);

    if (VERBOSITY) {
        printf("Maze Runner (seed=%u)\n", seed);
        printf("Flag placed at [%d,%d,%d]\n", FLAG.f, FLAG.x, FLAG.y);
    }

    bool game_over=false;
    Player* order[3]={&A,&B,&C};

    for (current_round=1; current_round<=MAX_ROUNDS && !game_over; ++current_round) {

        /* Rule 6 – every N rounds randomize unidirectional stairs; keep for D rounds */
        if ((current_round-1)%STAIRS_CHANGE_EVERY_ROUNDS==0) {
            randomize_stairs_oneway();
            if (VERBOSITY) printf("\n== Round %d: STAIRS become UNIDIRECTIONAL for %d rounds ==\n",
                                   current_round, STAIRS_ONEWAY_DURATION_ROUNDS);
        } else if (stair_oneway_rounds_left==0) {
            restore_stairs_bidir();
        }

        for (int pi=0; pi<3 && !game_over; ++pi) {
            Player* P = order[pi];
            Player* Q1 = order[(pi+1)%3];
            Player* Q2 = order[(pi+2)%3];

            if (VERBOSITY) printf("\n-- %c turn (Round %d) -- MP=%d  Pos=[%d,%d,%d] Dir=%s  ",
                                  P->name, current_round, P->mp, P->pos.f,P->pos.x,P->pos.y, dir_name(P->dir));

            /* Skip turns for Food Poisoning; after skipping, draw another Bawana cell & apply */
            if (P->turns_skipped > 0) {
                P->turns_skipped--;
                if (VERBOSITY) printf("Food Poisoning: skip (remaining %d)\n", P->turns_skipped);
                if (P->turns_skipped == 0) {
                    if (VERBOSITY) printf("  -> Food Poisoning ended. Draw new Bawana effect.\n");
                    apply_bawana_effect(P);
                }
                continue;
            } else {
                if (VERBOSITY) printf("\n");
            }

            /* Movement die roll */
            int move_die = rnd(1,6);

            /* If not entered, must roll 6 to enter */
            if (!P->in_maze) {
                if (VERBOSITY) printf("  Rolled movement=%d (needs 6 to enter)\n", move_die);
                if (move_die == 6) {
                    P->pos = P->first_cell;
                    P->in_maze = true;
                    /* Apply cell effects on entry cell */
                    apply_cell_effects(P, &G[P->pos.f][P->pos.x][P->pos.y]);
                    if (VERBOSITY) printf("  -> Enters maze at [%d,%d,%d], MP=%d\n",
                                          P->pos.f,P->pos.x,P->pos.y,P->mp);
                    P->throws_since_dir = 1; /* counts toward the 4-throw cadence */
                } else {
                    /* blocked “move” (no movement) does not consume 2 MP by spec; only wall blocks do */
                    continue;
                }
            } else {
                /* Decide if direction die is rolled this throw */
                bool will_roll_dir = (P->throws_since_dir==3);
                int dir_face = 0;

                if (P->disoriented_left > 0) {
                    /* Disoriented overrides direction dice for next N throws */
                    Dir newdir = (Dir)rnd(0,3);
                    if (VERBOSITY) printf("  Disoriented: overriding direction -> %s\n", dir_name(newdir));
                    P->dir = newdir;
                    P->disoriented_left--;
                    P->throws_since_dir = (P->throws_since_dir+1)%4;
                } else if (will_roll_dir) {
                    /* Roll direction die on 4th throw */
                    /* inside your turn logic, on the 4th throw */
                    int dir_face = rnd(1,6);
                    int nd = parse_direction_die(dir_face);
                    if (nd >= 0) {
                        P->dir = (Dir)nd;
                        printf("  Direction die face=%d -> %s\n", dir_face, dir_name(P->dir));
                    } else {
                        printf("  Direction die face=%d -> (empty) keep %s\n", dir_face, dir_name(P->dir));
                    }

                    /* paranoid guard, just in case */
                    if (!is_valid_dir(P->dir)) P->dir = NORTH;
                    P->throws_since_dir = 0; /* reset after 4th throw */
                } else {
                    P->throws_since_dir++;
                }

                /* Triggered doubles movement for next N throws */
                int steps = move_die;
                if (P->triggered_left > 0) {
                    steps *= 2;
                    P->triggered_left--;
                    if (VERBOSITY) printf("  Triggered: doubling steps this turn. Move=%d\n", steps);
                } else {
                    if (VERBOSITY) printf("  Move steps=%d\n", steps);
                }

                /* Attempt full move; if blocked by wall/edge, pay 2 MP (Rule 12) */
                bool teleported=false;
                Pos before = P->pos;
                int  mp_before = P->mp;
                bool ok = attempt_full_move(P, steps, &A,&B,&C, &teleported);

                if (!ok) {
                    /* could not complete all steps → pay 2 MP and stay */
                    P->pos = before;
                    P->mp  = mp_before;
                    pay_blocked_cost(P);
                } else if (!teleported) {
                    if (VERBOSITY) printf("  -> Landed at [%d,%d,%d], MP=%d\n", P->pos.f,P->pos.x,P->pos.y,P->mp);
                }

                /* End-of-move capture (only if you *land* on the same cell) */
                if (!teleported) {
                    check_capture(P, Q1);
                    check_capture(P, Q2);
                }
            }

            /* Win check: capture the flag by landing exactly on it (after movement/transport) */
            if (P->in_maze && same_pos(P->pos, FLAG)) {
                printf("\n*** %c captured the FLAG at [%d,%d,%d]! GAME OVER in round %d. ***\n",
                       P->name, FLAG.f,FLAG.x,FLAG.y, current_round);
                game_over = true;
            }
        }

        if (stair_oneway_rounds_left > 0) {
            stair_oneway_rounds_left--;
            if (stair_oneway_rounds_left == 0) {
                restore_stairs_bidir();
                if (VERBOSITY) printf("\n== Stair one-way phase ended; stairs are bidirectional again ==\n");
            }
        }
    }

    if (!game_over) {
        printf("\n*** No winner within %d rounds. ***\n", MAX_ROUNDS);
    }
    return 0;
}
