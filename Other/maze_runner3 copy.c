#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <limits.h>
#include "types.h"


const int dirRows[4] = {-1, 0, 1, 0};
const int dirCol[4]   = {0, 1, 0, -1};


Cell BOARD[FLOORS][ROWS][COLUMNS];
BawanaType BAWANA_MAP[BAWANA_ROW_END-BAWANA_ROW_START+1][BAWANA_COL_END-BAWANA_COL_START+1];
Player PLAYERS[NUM_PLAYERS];

// implement file based approach later
int n_stairs = 6;
int n_poles = 2;
int n_walls = 1;

StairState STAIRS[CELLS] = {
    {{0,4,5, 2,0,10}, BI},
    {{1,7,3, 2,7,8}, BI},
    {{0,2,20, 1,2,18}, BI},
    {{1,1,20, 2,1,16}, BI},
    {{0,9,17, 1,9,16}, BI},
    {{0,4,5, 1,8,10}, BI},
    {{0,9,17, 1,9,16}, BI},
};

Pole POLES[CELLS] = {
   
    {0,2,5,24}, // poll start low and goes up
    {0,1,9,7},
};

int WALLS[][5] = {
// floor, s_row, s_col, e_row, e_col
    {1,0,2,8,2}
};


Position FLAG;

int game_round = 0;
int stairs_duration = 0;

// functions
void apply_cell_effect(Player *p, const Cell *cell);
void setup_players();
bool same_pos(Position a, Position b);
void place_flag();
void blocked_cost(Player *P);
bool attempt_move(Player *P, int steps, bool *sent_to_bawana);
void check_capture(Player* P);
Cell *check_pole(Cell *current);
Cell *check_stairs(Cell *current);
bool can_use_stair(StairState *stair_state, Position current_pos);
int man_best_distance(Position pos1, Position pos2);
void initialize_poles();
void initialize_stairs();
void stairs_bidirectional();
void stairs_up();
void randomize_stairs_direction();
void apply_bawana(Player *p);
void exit_bawana(Player *p);
BawanaType random_bawana_cell ();
void initialize_bawana();
void consumables_randomize();
void add_wall(int floor, int col1, int col2, int row1, int row2);
int min(int a, int b);
int max(int a, int b);
void init_cells();
void init_walls();
void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul);
bool is_in_board (int row, int col);
bool is_cell_available(int floor, int row, int col);
Cell* get_cell (int floor, int row, int col);
int set_direction(int die);
int rand_int(int low, int high);
bool is_valid_direction(int d);
char* direction_name(Direction dir);


// main
int main() {
    srand((unsigned) time(NULL));

    init_cells();
    init_walls();
    consumables_randomize();
    initialize_bawana();
    initialize_stairs();
    initialize_poles();
    place_flag();

    Player A,B,C;
    PLAYERS[0] =A;
    PLAYERS[1] =B;
    PLAYERS[2] =C;
    setup_players();

    printf("MAZE RUNNER\n\n");
    printf("Flag Placed at [%d,%d,%d]\n\n", FLAG.floor,FLAG.row,FLAG.col);

    bool game_over = false;


    for (game_round=0; game_round<GAME_ROUNDS && !game_over; game_round++){
        if (game_round%STAIRS_CHANGE_EVERY_ROUNDS==0 && game_round!=0) {
            randomize_stairs_direction();
            char* dir_str = (STAIRS[0].mode==UP) ? "up" : "down";
            printf("Stairs direction changed to %s", dir_str);
        }
        // mis undestanding of problem all times stairs are unidirectional
        // else if (stairs_duration==0) {
        //     stairs_bidirectional();
        // }

        for (int player_round = 0; player_round<NUM_PLAYERS; player_round++){
            Player *P = &PLAYERS[player_round];
            
            printf("== Round %d, Player %c's turn, %dMP  Position [%d,%d,%d] Direction %s \n",game_round+1, P->name, P->mp, P->position.floor,P->position.row,P->position.col, direction_name(P->direction));

            if (P->turns_skipped > 0){
                P->turns_skipped--;
                printf("Food poisoning: Skipping turn, %d remaining\n", P->turns_skipped);
                if (P->turns_skipped==0) {
                    printf("Food poisoning over, sending back to bawana\n");
                    apply_bawana(P);
                }
                continue;
            } 
            int move_die = rand_int(1,6);
            printf("Player %c rolls move die to %d\n", P->name, move_die);
            if (!P->in_maze) {
                if (move_die==6) {
                    P->position = P->start_cell;
                    P->in_maze = true;
                    apply_cell_effect(P,get_cell(P->start_cell.floor, P->start_cell.row,P->start_cell.col));
                    printf("Player %c enters maze at [%d,%d,%d] with MP-%d Direction %s\n", P->name,P->position.floor,P->position.row,P->position.col,P->mp, direction_name(P->direction));
                    P->throws_since_dir_change=(P->throws_since_dir_change+1)%4;
                } else {
                    if (DEBUG) printf("Player %c Can't enter \n", P->name);
                    continue;
                }
            } else {
                bool will_roll_dir = (P->throws_since_dir_change==3);
                if (P->disoriented_left > 0) {
                    Direction new_dir = (Direction) rand_int(0,3);
                    printf("Player %c is Disoriented\n", P->name);
                    P->direction = new_dir;
                    P->disoriented_left--;
                    P->throws_since_dir_change = (P->throws_since_dir_change+1)%4;
                } else if (will_roll_dir) {
                    int dir_face = rand_int(1,6);
                    int is_dir_change = is_valid_direction(dir_face);
                    if (is_dir_change) {
                        P->direction = (Direction) dir_face;
                        printf("Direction DIe face=%d so direction will be %s\n", dir_face, direction_name(P->direction));
                    } else {
                        printf("Direction die face= %d, keep %s\n", dir_face, direction_name(P->direction));
                    }

                    if (!is_valid_direction(P->direction)) P->direction=NORTH;
                    P->throws_since_dir_change=0;
                } else {
                    P->throws_since_dir_change++;
                }
                
                int steps=move_die;
                if (P->triggered_left > 0) {
                    steps *= 2;
                    P->triggered_left--;
                    printf("Player %c is Triggered\n", P->name);
                } else {
                    if (DEBUG) printf("Attempting %i steps move\n", steps);
                }

                bool bawana=false;
                Position before = P->position;
                int mp_bofore = P->mp;
                bool move = attempt_move(P,steps,&bawana);
                if (!move) {
                    P->position = before;
                    P->mp = mp_bofore;
                    blocked_cost(P);
                } else if (!bawana) {
                    check_capture(P);
                }

                // win check
                if (P->in_maze && same_pos(P->position, FLAG)) {
                    printf("\n == %c captured flag at [%d,%d,%d]! Game over in round %d.\n",P->name, P->position.floor, P->position.row, P->position.col, game_round+1);
                    game_over=true;
                }


            }

        }

    }
    if (!game_over) {
        printf("No winner within %d rounds\n", GAME_ROUNDS);
    }
    return 0;
    
}


/*
returns a randomized integer value between low and high (inclusive)
*/

int rand_int(int low, int high) {
    return low + (rand() % (high-low +1));
}


// returns true if direction valid

bool is_valid_direction(int d) {
    return d>=0 && d<=3;
}

// returns string of direction for printing

char* direction_name(Direction dir) {
    if (!is_valid_direction(dir)) return "Not Valid";
    switch (dir) {
        case NORTH: return "NORTH";
        case EAST: return "EAST";
        case WEST: return "WEST";
        case SOUTH: return "SOUTH";
    }
}

// set direction
int set_direction(int die) {
    switch(die) {
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1;
    }
}

/*
returns cell pointer taking floor, row, column
*/
Cell* get_cell (int floor, int row, int col) {
    if (!is_in_board(row, col)) return NULL;
    return &BOARD[floor][row][col];
}

/*
returns true if the cell is within the board (not considering the restricted areas, only outer box)
*/
bool is_in_board (int row, int col) {
    return (row >= 0 && row < ROWS && col >= 0 && col < COLUMNS);
}

bool is_cell_available(int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(floor, row, col);
    if (cell->kind==NORMAL) return true;
    return false;
}

void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul) {
    cell->kind = kind;
    cell->consumable = consumables;
    cell->bonus_add = add;
    cell->bonus_mul = mul;
    for (int i = 0; i< CELLS; i++){
        cell->stair[i] = NULL;
    }
    cell->num_stairs=0;
    cell->pole = NULL;
}

void init_cells(){
    for (int f=0; f<FLOORS; f++) {
        for (int r = 0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++){
                Cell *cell = &BOARD[f][r][c];
                Coord coordinates;
                coordinates.col =c;
                coordinates.row = r;
                coordinates.floor = f;
                cell->coord = coordinates;
                if (f==0){
                    if(r>=START_AREA_ROW_START && r <= START_AREA_ROW_END && c >= START_AREA_COL_START && c<=START_AREA_COL_END){
                        set_cell(cell, START, -1, 0, 1);
                        continue;
                    }
                    if (r>=BAWANA_ROW_START && r <= BAWANA_ROW_END && c >= BAWANA_COL_START && c <= BAWANA_COL_END){
                        set_cell(cell, BAWANA, -1, 0, 1);
                        continue;
                    }
                    if (r == BAWANA_ROW_START-1 && c >= BAWANA_COL_START-1 && c <= BAWANA_COL_END) {
                        set_cell(cell, WALL, -1, 0, 1);
                        continue;
                    }
                    if (r >= BAWANA_ROW_START && r <= BAWANA_ROW_END &&  c == BAWANA_COL_START-1) {
                        set_cell(cell, WALL, -1, 0, 1);
                        continue;
                    }
                }
                if (f==1){
                    if(!(c >= F1_RECT1_COL_START && c<=F1_RECT1_COL_END)){
                        set_cell(cell, NONE, -1, 0, 1);
                        continue;
                    }
                    if (!(r>=F1_BRIDGE_ROW_START && r <= F1_BRIDGE_ROW_END&& c >= F1_BRIDGE_COL_START && c <= F1_BRIDGE_COL_END)){
                        set_cell(cell, NONE, -1, 0, 1);
                        continue;
                    }
                }
                if (f==2){
                    if(!(c >= F2_RECT_COL_START && c<=F2_RECT_COL_END)){
                        set_cell(cell,NONE,-1,0,1);
                        continue;
                    }
                }
                set_cell(cell,NORMAL, -1, 0, 1);
            }
        }
    }
}

int min(int a, int b) {
    if (a>b) return b;
    return a;
}

int max(int a, int b) {
    if (a>b) return a;
    return b;
}

void add_wall(int floor, int row1,  int col1, int row2, int col2) {
    if (!(is_in_board(row1, col1)) || !(is_in_board(row2,col2))) return;
    if (!((row1==row2) || (col1==col2))) return;
    if (row1==row2){
        for (int i = min(col1,col2); i <= max(col1, col2); i++){
            BOARD[floor][row1][i].kind = WALL;
        }
    }
    if (col1==col2){
        for (int i = min(row1,row2); i <= max(row1, row2); i++){
            BOARD[floor][i][col1].kind = WALL;
        }
    }
}

void init_walls(){
    for (int i=0; i < n_walls; i++){
        add_wall(WALLS[i][0],WALLS[i][1],WALLS[i][2], WALLS[i][3], WALLS[i][4]);
    }
}

void consumables_randomize(){
    Cell *cells[CELLS];
    int n_active = 0;
    for (int f=0; f < FLOORS; f++) {
        for (int r=0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++) {
                if (!(is_cell_available(f,r,c))) continue;
                cells[n_active++] = &BOARD[f][r][c];
            }
        }
    }

    int con_none = (int) (0.25*n_active);
    int con_1 = (int) (0.35*n_active);
    int bonus_12 = (int) (0.25*n_active);
    int bonus_35 = (int) (0.10*n_active);
    int mul_23 = n_active - (con_none + con_1 + bonus_12 + bonus_35);


    int added_cells =0;
    #define EMPTY(random_cell) ((random_cell)->consumable == -1 && (random_cell)->bonus_add == 0 && (random_cell)->bonus_mul == 1)
    // consumables 0
    int i = 0;
    while (i < con_none && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable =0;
        i++, added_cells++; 
    }
    // consumable 1-4
    i=0;

    while (i < con_1 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable = rand_int(1,4);
        i++, added_cells++; 
    }
    
    // bonus 1,2
    i=0;
    while (i < bonus_12 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(1,2);
        i++, added_cells++; 
    }

    // bonus 3..5
    i=0;
    while (i < bonus_35 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(3,5);
        i++, added_cells++; 
    }
    // mul 2,3
    i=0;
    while (i < mul_23 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_mul = rand_int(2,3);
        i++, added_cells++; 
    }

}

// bawana

void initialize_bawana() {
    BawanaType bw_cells[BAWANA_CELLS];
    int food_p = 3, trig = 3, disor=3, happy=3, points=4;
    int added_cells =0;
    for (int i=0; i < BAWANA_CELLS; i++) {
        bw_cells[i] = POINTS;
    }
    // food poisoning
    int i = 0;
    while (i < food_p && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=FOOD_P;
        i++, added_cells++;
    }
    // disspor
    i = 0;
    while (i < trig && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=TRIG;
        i++, added_cells++; 
    }
    // happy
    i = 0;
    while (i < happy && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=HAPPY;
        i++, added_cells++; 
    }

    added_cells=0;
    for (int row=0; row<=BAWANA_ROW_END-BAWANA_ROW_START; row++) {
        for (int col=0; col<=BAWANA_COL_END-BAWANA_COL_START; col++){
            BAWANA_MAP[row][col] = bw_cells[added_cells++];
        }
    }

}

BawanaType random_bawana_cell () {
    int ran_col = rand_int(0, BAWANA_COL_END-BAWANA_COL_START);
    int ran_row = rand_int(0, BAWANA_ROW_END-BAWANA_ROW_START);

    return BAWANA_MAP[ran_row][ran_col];
}

void exit_bawana(Player *p) {
    p->position= (Position) {0, BAWANA_EXIT_ROW, BAWANA_EXIT_COL};
    p->direction=NORTH;
}

void apply_bawana(Player *p) {
    BawanaType type = random_bawana_cell();
    if (type==FOOD_P) {
        p->turns_skipped=3;
        if (DEBUG) printf("  Bawana food Poisoning. 3 turns will be skipped then get returned to bawana\n");
    } else if (type==DISOR) {
        p->mp += 50;
        p->disoriented_left = DISORIENTED_THROWS;
        exit_bawana(p);
        if (DEBUG) printf("  Bawana Disoriented (+50mp) %d throws will be on random directions\n", DISORIENTED_THROWS);
    } else if (type==TRIG) {
        p->mp += 50;
        p->triggered_left = TRIGGERED_THROWS;
        exit_bawana(p);
        if (DEBUG) printf("  Bawana Triggered (+50mp) %d throws will be on doubled\n", TRIGGERED_THROWS);
    } else if (type==HAPPY) {
        p->mp += 200;
        exit_bawana(p);
        if (DEBUG) printf("  Bawana Happy got 200mp\n");
    } else {
        int pts = rand_int(10,100);
        p->mp += pts;
        exit_bawana(p);
        if (DEBUG) printf("  Bawana normal cell, got %d points\n", pts);
    }
}

void randomize_stairs_direction() {
    for (int i=0; i <n_stairs; i++) {
        int random_01 = rand_int(0,1);
        STAIRS[i].mode = (random_01==0) ? UP : DOWN;
    }
    stairs_duration = STAIRS_DURATION;  // set stairs duration globally
    // handled globally
}


void stairs_bidirectional() {
    for (int i=0; i < n_stairs; i++) {
        STAIRS[i].mode = BI;
    }
}
void stairs_up() {
    for (int i=0; i < n_stairs; i++) {
        STAIRS[i].mode = UP;
    }
}

void initialize_stairs() {
    for (int i = 0; i < n_stairs; i++) {
        StairState *stair_state = &STAIRS[i];
        Stair stair = stair_state->stair;
        if (!(is_cell_available(stair.e_floor, stair.e_row, stair.e_col) && is_cell_available(stair.s_floor, stair.s_row, stair.s_col))) continue;;
       
        Cell *start_cell = get_cell(stair.s_floor, stair.s_row, stair.s_col);
        if (start_cell != NULL) {
            start_cell->stair[start_cell->num_stairs++] = stair_state;
        }


        if (!(stair.s_floor == stair.e_floor && stair.s_row == stair.e_row && stair.s_col == stair.e_col)) {
            Cell *end_cell = get_cell(stair.e_floor, stair.e_row, stair.e_col);
            if (end_cell != NULL) {
                end_cell->stair[end_cell->num_stairs++] = stair_state;
            }
        }
    }

    // stairs_bidirectional();
    stairs_up();
}


/*
adds poles to all cells including ones below the starting one
example if pole is starting at 3,0,3,10 it is be available to  2 nd floor too at 1,3,10 and in 3rd floor 2,3,10
*/
void initialize_poles() {
    for (int i = 0; i < n_poles; i++) {
        Pole *pole = &POLES[i];
        bool valid = true;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(floor, pole->row, pole->col);
            if (!is_cell_available(cell->coord.floor,cell->coord.row,cell->coord.col)){
                valid = false;
                break;
            } 
        }
        if (!valid) continue;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(floor, pole->row, pole->col);
            if (cell != NULL) {
                // If cell already has a pole, choose the one with wider range
                if (cell->pole != NULL) {
                    if (pole->s_floor <= cell->pole->s_floor && pole->e_floor >= cell->pole->e_floor){
                        cell->pole = pole; // New pole has wider range
                    }
                    // Keep existing pole if it has wider or equal range
                } else {
                    cell->pole = pole; // No existing pole, assign this one
                }
            }
        }
    }
}


int man_best_distance(Position pos1, Position pos2) {
    return (abs(pos1.floor-pos2.floor)+1) * (abs(pos1.row - pos2.row) + abs(pos1.col - pos2.col));
}

bool can_use_stair(StairState *stair_state, Position current_pos) {
    Stair *stair = &stair_state->stair;

    //  start position
    if (current_pos.floor == stair->s_floor &&
        current_pos.row == stair->s_row &&
        current_pos.col == stair->s_col) {
        return (stair_state->mode == BI || stair_state->mode == UP);
    }

    // end position
    if (current_pos.floor == stair->e_floor &&
        current_pos.row == stair->e_row &&
        current_pos.col == stair->e_col) {
        return (stair_state->mode == BI || stair_state->mode == DOWN);
    }

    return false;
}

Cell *check_stairs(Cell *current) {
    if (current->num_stairs == 0) return NULL;
    Position current_pos = {current->coord.floor, current->coord.row, current->coord.col};
    int e_floor = current->stair[0]->stair.e_floor;
    int s_floor = current->stair[0]->stair.s_floor;
    int e_row = current->stair[0]->stair.e_row;
    int e_col = current->stair[0]->stair.e_col;
    Cell *end_cell = get_cell(e_floor, e_row, e_col);
    if (current->num_stairs == 1) {
        if (!(can_use_stair(current->stair[0], current_pos))) return NULL;
        return end_cell;  
    } 

    Cell *best_destination = end_cell;
    int best_distance = man_best_distance((Position){end_cell->coord.floor, end_cell->coord.row, end_cell->coord.col}, FLAG);

    // Check each stair available at this cell
    for (int i = 0; i < current->num_stairs; i++) {
        StairState *stair_s = current->stair[i];

        if (!can_use_stair(stair_s, current_pos)) continue;

        // Determine destination position
        Position dest_pos;
        if (current_pos.floor == stair_s->stair.s_floor &&
            current_pos.row == stair_s->stair.s_row &&
            current_pos.col == stair_s->stair.s_col) {
            // start to end
            dest_pos = (Position){stair_s->stair.e_floor, stair_s->stair.e_row, stair_s->stair.e_col};
        } else {
            // end to start
            dest_pos = (Position){stair_s->stair.s_floor, stair_s->stair.s_row, stair_s->stair.s_col};
        }

        Cell *dest_cell = get_cell(dest_pos.floor, dest_pos.row, dest_pos.col);
        if (!is_cell_available(dest_pos.floor, dest_pos.row, dest_pos.col)) {
            continue;
        }

        //distance to FLAG
        int distance = man_best_distance(dest_pos, FLAG);

        if (distance < best_distance) {
            best_destination = dest_cell;
            best_distance = distance;
        }
    }

    return best_destination;
}


// poles
Cell *check_pole(Cell *current) {
    if (!(current->pole)) return NULL;
    if (current->pole && current->pole->e_floor >= current->coord.floor){
        return get_cell(current->pole->e_floor,current->pole->row,current->pole->col);
    }
    return NULL;
}

// movement


void apply_cell_effect(Player *p, const Cell *cell){
    if (cell->consumable >= 0) p->mp -= cell->consumable;
    else if (cell->bonus_add > 0) p->mp += cell->bonus_add;
    else if (cell->bonus_mul > 1) p->mp *= cell->bonus_mul;
} 



void check_capture(Player* P) {
    for (int i = 0; i < NUM_PLAYERS; i++) {
        Player *Q = &PLAYERS[i];
        if (!Q->in_maze) continue;
        if (P->position.floor==Q->position.floor && P->position.row==Q->position.row && P->position.col == Q->position.col) {
            if (DEBUG) printf(" %c captures %c! %c sent to starting cell\n", P->name, Q->name, Q->name);
            Q->in_maze=false;
        }
    }
}




bool attempt_move(Player *P, int steps, bool *sent_to_bawana) {
    *sent_to_bawana = false;

    Position start = P->position;
    int mp_start = P->mp;

    Position current = start;
    int remaining = steps;

    while (remaining > 0) {
        // 2nd check even in current
        if (!is_cell_available(current.floor,current.row,current.col) || !is_valid_direction(P->direction)){
            if (DEBUG) printf("Blocked at [%d,%d,%d]\n", current.floor, current.row, current.col);
            P->position=start;
            P->mp = mp_start;
            return false;
        }
        Position next = (Position) {
            current.floor,
            current.row + dirRows[P->direction],
            current.col + dirCol[P->direction]
        };
        // next cell check
        if (!is_cell_available(next.floor,next.row, next.col) || !is_valid_direction(P->direction)) {
            if (DEBUG) printf("Blocked at [%d,%d,%d]\n", current.floor, current.row, current.col);
            P->position = start;
            P->mp = mp_start;
            return false;
        }

        current = next;
        Cell *cell_c = get_cell(current.floor, current.row, current.col);

        Cell *jumped = check_stairs(cell_c);
        if (jumped) {
            if (DEBUG) printf("STAIRS: Player %c took stairs at [%d,%d,%d] to [%d,%d,%d]\n",P->name, current.floor, current.row, current.col, jumped->coord.floor, jumped->coord.row, jumped->coord.col );
            current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
        } else {
            jumped = check_pole(cell_c);
            if (jumped) {
                if (DEBUG) printf("POLE: Player %c took ple at [%d,%d,%d] to [%d,%d,%d]\n",P->name, current.floor, current.row, current.col, jumped->coord.floor, jumped->coord.row, jumped->coord.col );
                current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
            }
        }

        apply_cell_effect(P, get_cell(current.floor, current.row, current.col));

        if (P->mp <= 0) {
            if (DEBUG) printf(" BAWANA: MP is low sent to bawana\n");
            P->position = current;
            apply_bawana(P);
            *sent_to_bawana=true;
            return true;
        }
        remaining--;

    }

    P->position = current;
    return true;
}

void blocked_cost(Player *P) {
    P->mp -= 2;
    if (DEBUG) printf("BLOCK: Move blocked. cost +2MP MP:%d\n", P->mp);
    if (P->mp <= 0) {
        if (DEBUG) printf(" BAWANA: MP is low sent to bawana\n");
        apply_bawana(P);
    }
}


// init

void place_flag() {
    while (true) {
        int floor = rand_int(0,2), row = rand_int(0,9), col = rand_int(0,24);
        if (!is_cell_available(floor,row,col)) continue;
        Cell *cell = get_cell(floor,row, col);
        if (cell->pole || cell->num_stairs > 0) continue;
        FLAG = (Position){floor,row,col};
        break; 
    }
}

void initialize_player(Player *player, char name, int start_row, int start_col, int first_row, int first_col, Direction dir){
    player->name = name;
    player->position = (Position) {0,start_row,start_col};
    player->start_cell = (Position) {0, first_row, first_col};
    player->direction = dir;
    player->in_maze = false;
    player->mp = 100;
    player->throws_since_dir_change=0;
    player->turns_skipped=0;
    player->disoriented_left=0;
    player->triggered_left=0;
}

void setup_players() {
    
    initialize_player(&PLAYERS[0],'A',6,12,5,12,NORTH);
    initialize_player(&PLAYERS[1],'B',9,8,9,7,WEST);
    initialize_player(&PLAYERS[2],'C',9,16,9,17,EAST);
    
}

bool same_pos(Position a, Position b) {
    return (a.floor==b.floor && a.row==b.row && a.col==b.col);
}

